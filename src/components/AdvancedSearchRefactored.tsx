"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { MultiSelect } from "@/components/ui/multi-select";
import { Filter, Plus, X, Search, ArrowDown } from "lucide-react";
import { convertFiltersToConditions, mergeFilterAndAdvancedConditions } from '@/lib/filterToConditionConverter';

// Simplified search condition interface - no operators needed
export interface SearchCondition {
  id: string;
  field: string;
  value: string | string[] | number | number[] | Date | Date[] | { from?: string; to?: string };
  logic?: 'AND' | 'OR';
}

interface AdvancedSearchProps {
  onSearch: (conditions: SearchCondition[]) => void;
  onClear: () => void;
  availableFields: Array<{
    fieldName: string;
    displayName: string;
    fieldType: string;
    filterType?: string;
    searchType?: string;
    isAdvancedSearchable?: boolean;
  }>;
  currentConditions?: SearchCondition[];
  metadata?: Record<string, string[]>;
  currentFilters?: Record<string, unknown>;
  showFilterIntegration?: boolean;
}

export default function AdvancedSearchRefactored({
  onSearch,
  onClear,
  availableFields,
  currentConditions = [],
  metadata = {},
  currentFilters = {},
  showFilterIntegration = true
}: AdvancedSearchProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [conditions, setConditions] = useState<SearchCondition[]>(currentConditions);

  // Filter fields to only show those marked as advanced searchable
  const searchableFields = availableFields.filter(f => f.isAdvancedSearchable);

  // Check if there are active filters that can be imported
  const hasActiveFilters = Object.values(currentFilters).some(value => 
    value !== undefined && value !== null && value !== '' && 
    (!Array.isArray(value) || value.length > 0)
  );

  // Update conditions when currentConditions prop changes
  useEffect(() => {
    setConditions(currentConditions);
  }, [currentConditions]);

  const addCondition = () => {
    const newCondition: SearchCondition = {
      id: `condition_${Date.now()}`,
      field: searchableFields[0]?.fieldName || '',
      value: '',
      logic: conditions.length > 0 ? 'AND' : undefined
    };
    setConditions([...conditions, newCondition]);
  };

  const removeCondition = (id: string) => {
    setConditions(conditions.filter(c => c.id !== id));
  };

  const updateCondition = (id: string, updates: Partial<SearchCondition>) => {
    setConditions(conditions.map(c => 
      c.id === id ? { ...c, ...updates } : c
    ));
  };

  const handleSearch = () => {
    const validConditions = conditions.filter(c => {
      if (typeof c.value === 'string') {
        return c.value.trim() !== '';
      }
      if (Array.isArray(c.value)) {
        return c.value.length > 0;
      }
      if (typeof c.value === 'object' && c.value !== null) {
        const obj = c.value as { from?: string; to?: string };
        return obj.from || obj.to;
      }
      return c.value !== undefined && c.value !== null;
    });

    onSearch(validConditions);
    setIsOpen(false);
  };

  const handleClear = () => {
    setConditions([]);
    onClear();
    setIsOpen(false);
  };

  const handleImportFilters = () => {
    const filterConditions = convertFiltersToConditions(currentFilters, availableFields);
    const mergedConditions = mergeFilterAndAdvancedConditions(filterConditions, conditions);
    setConditions(mergedConditions);
  };

  // Render appropriate input component based on field's searchType
  const renderValueInput = (condition: SearchCondition, field: any) => {
    const searchType = field.searchType || field.fieldType;

    switch (searchType) {
      case 'date_range':
        const dateValue = typeof condition.value === "object" ? condition.value : { from: '', to: '' };
        return (
          <DateRangePicker
            startDate={dateValue.from || ''}
            endDate={dateValue.to || ''}
            onStartDateChange={(date) => updateCondition(condition.id, {
              value: { ...dateValue, from: date }
            })}
            onEndDateChange={(date) => updateCondition(condition.id, {
              value: { ...dateValue, to: date }
            })}
            placeholder={`Select ${field.displayName.toLowerCase()} range`}
          />
        );

      case 'date':
        return (
          <Input
            type="date"
            value={typeof condition.value === "string" ? condition.value : ''}
            onChange={(e) => updateCondition(condition.id, { value: e.target.value })}
            className="w-full"
          />
        );

      case 'exact':
      case 'select':
        if (field.fieldType === 'boolean') {
          return (
            <Select
              value={typeof condition.value === "string" ? condition.value : '__none__'}
              onValueChange={(value) => updateCondition(condition.id, { 
                value: value === "__none__" ? '' : value 
              })}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select value" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__none__">Select value</SelectItem>
                <SelectItem value="true">Yes</SelectItem>
                <SelectItem value="false">No</SelectItem>
              </SelectContent>
            </Select>
          );
        }

        // For select fields with metadata options
        if (field.filterType === 'select' || field.filterType === 'multi_select') {
          const options = metadata[field.fieldName] || [];
          const validOptions = options.filter(option => 
            typeof option === 'string' && option.trim() !== ''
          );

          if (field.filterType === 'multi_select') {
            return (
              <MultiSelect
                options={validOptions.map(option => ({ value: option, label: option }))}
                value={Array.isArray(condition.value) ? condition.value : []}
                onValueChange={(value) => updateCondition(condition.id, { value })}
                placeholder={`Select ${field.displayName}`}
              />
            );
          }

          return (
            <Select
              value={typeof condition.value === "string" ? condition.value : '__all__'}
              onValueChange={(value) => updateCondition(condition.id, { 
                value: value === "__all__" ? '' : value 
              })}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={`Select ${field.displayName}`} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">All</SelectItem>
                {validOptions.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          );
        }
        break;

      case 'range':
        // For numeric range inputs
        const rangeValue = typeof condition.value === "object" ? condition.value : { from: '', to: '' };
        return (
          <div className="flex gap-2 items-center">
            <Input
              type="number"
              placeholder="Min"
              value={rangeValue.from || ''}
              onChange={(e) => updateCondition(condition.id, {
                value: { ...rangeValue, from: e.target.value }
              })}
              className="flex-1"
            />
            <span className="text-gray-400">to</span>
            <Input
              type="number"
              placeholder="Max"
              value={rangeValue.to || ''}
              onChange={(e) => updateCondition(condition.id, {
                value: { ...rangeValue, to: e.target.value }
              })}
              className="flex-1"
            />
          </div>
        );

      case 'contains':
      case 'starts_with':
      case 'ends_with':
      default:
        return (
          <Input
            placeholder={`Enter ${field.displayName.toLowerCase()}`}
            value={typeof condition.value === "string" ? condition.value : ''}
            onChange={(e) => updateCondition(condition.id, { value: e.target.value })}
            className="w-full"
            showClearButton={true}
            onClear={() => updateCondition(condition.id, { value: '' })}
          />
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="h-8 text-xs">
          <Filter className="mr-1 h-3 w-3" />
          Advanced Search
          {currentConditions.length > 0 && (
            <Badge variant="secondary" className="ml-2 h-4 text-xs">
              {currentConditions.length}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Advanced Search
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Filter Integration Section */}
          {showFilterIntegration && hasActiveFilters && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <ArrowDown className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">
                    Import Current Filters
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleImportFilters}
                  className="text-blue-700 border-blue-300 hover:bg-blue-100"
                >
                  Add Filters
                </Button>
              </div>
              <p className="text-xs text-blue-600 mt-1">
                Populate search conditions from your current filter selections
              </p>
            </div>
          )}

          {/* Search Conditions */}
          {conditions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No search conditions added yet.</p>
              <p className="text-sm">Click "Add Condition" to start building your search.</p>
              {hasActiveFilters && (
                <p className="text-sm text-blue-600 mt-2">
                  Or use the "Add Filters" button above to populate from current filters.
                </p>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {conditions.map((condition, index) => {
                const field = searchableFields.find(f => f.fieldName === condition.field);
                if (!field) return null;

                return (
                  <div key={condition.id} className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex items-center gap-3 mb-3">
                      {index > 0 && (
                        <Select
                          value={condition.logic || 'AND'}
                          onValueChange={(value) => updateCondition(condition.id, {
                            logic: value as 'AND' | 'OR'
                          })}
                        >
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="AND">AND</SelectItem>
                            <SelectItem value="OR">OR</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeCondition(condition.id)}
                        className="ml-auto text-red-600 hover:text-red-800 hover:bg-red-50"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {/* Field Selection */}
                      <div>
                        <label className="block text-sm font-medium mb-1">Field</label>
                        <Select
                          value={condition.field}
                          onValueChange={(value) => updateCondition(condition.id, {
                            field: value,
                            value: '' // Reset value when field changes
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {searchableFields.map(field => (
                              <SelectItem key={field.fieldName} value={field.fieldName}>
                                {field.displayName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Value Input */}
                      <div>
                        <label className="block text-sm font-medium mb-1">Value</label>
                        {renderValueInput(condition, field)}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          <div className="flex justify-between items-center pt-4 border-t">
            <Button
              variant="outline"
              onClick={addCondition}
              className="flex items-center gap-2"
              disabled={searchableFields.length === 0}
            >
              <Plus className="h-4 w-4" />
              Add Condition
            </Button>

            <div className="flex gap-2">
              <Button variant="ghost" onClick={handleClear}>
                Clear All
              </Button>
              <Button
                onClick={handleSearch}
                disabled={conditions.length === 0}
              >
                <Search className="mr-2 h-4 w-4" />
                Search
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
